# AI Companion System - Development Makefile

.PHONY: help install install-dev setup test test-unit test-integration test-performance lint format clean run run-api run-gradio docker-build docker-run docker-clean

# Default target
help:
	@echo "AI Companion System - Available Commands:"
	@echo ""
	@echo "Setup & Installation:"
	@echo "  install          Install production dependencies"
	@echo "  install-dev      Install development dependencies"
	@echo "  setup            Complete development setup"
	@echo ""
	@echo "Development:"
	@echo "  run              Run the system (Gradio interface)"
	@echo "  run-api          Run API server only"
	@echo "  run-gradio       Run Gradio interface only"
	@echo "  test-system      Run system validation tests"
	@echo "  health-check     Run comprehensive health check"
	@echo ""
	@echo "Testing:"
	@echo "  test             Run all tests"
	@echo "  test-unit        Run unit tests"
	@echo "  test-integration Run integration tests"
	@echo "  test-performance Run performance tests"
	@echo "  test-coverage    Run tests with coverage report"
	@echo ""
	@echo "Code Quality:"
	@echo "  lint             Run linting (flake8, mypy)"
	@echo "  format           Format code (black, isort)"
	@echo "  check            Run all quality checks"
	@echo ""
	@echo "Docker:"
	@echo "  docker-build     Build Docker image"
	@echo "  docker-run       Run Docker container"
	@echo "  docker-clean     Clean Docker resources"
	@echo ""
	@echo "Maintenance:"
	@echo "  clean            Clean temporary files"
	@echo "  clean-data       Clean data directories"
	@echo "  backup-data      Backup data directories"

# Setup & Installation
install:
	pip install -r requirements.txt

install-dev:
	pip install -r requirements-dev.txt

install-editable:
	pip install -e .

install-editable-dev:
	pip install -e ".[dev]"

setup: install-dev
	@echo "Setting up development environment..."
	@mkdir -p data/logs data/db data/cache
	@cp .env.example .env 2>/dev/null || echo ".env already exists"
	@echo "✅ Development environment ready!"
	@echo "⚠️  Please edit .env file with your API keys"

# Development
run:
	python -m src.ai_companion.main

run-api:
	uvicorn src.ai_companion.interfaces.api:app --host 0.0.0.0 --port 8000 --reload

run-gradio:
	python -c "import asyncio; from src.ai_companion.main import main; asyncio.run(main())"

test-system:
	python test_system.py

health-check:
	python scripts/health_check.py

# Testing
test:
	pytest tests/ -v

test-unit:
	pytest tests/unit/ -v

test-integration:
	pytest tests/integration/ -v

test-performance:
	pytest tests/performance/ -v

test-coverage:
	pytest --cov=src/ai_companion --cov-report=html --cov-report=term

# Code Quality
lint:
	flake8 src/ tests/
	mypy src/

format:
	black src/ tests/
	isort src/ tests/

check: lint test
	@echo "✅ All quality checks passed!"

# Docker
docker-build:
	docker build -t ai-companion-system .

docker-run:
	docker run -p 7860:7860 -p 8000:8000 --env-file .env ai-companion-system

docker-clean:
	docker system prune -f
	docker rmi ai-companion-system 2>/dev/null || true

# Maintenance
clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	find . -type f -name ".coverage" -delete
	rm -rf htmlcov/
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/

clean-data:
	@echo "⚠️  This will delete all data! Press Ctrl+C to cancel, Enter to continue..."
	@read
	rm -rf data/db/*.db
	rm -rf data/logs/*.log
	rm -rf data/cache/*

backup-data:
	@mkdir -p backups
	@tar -czf backups/data-backup-$(shell date +%Y%m%d-%H%M%S).tar.gz data/
	@echo "✅ Data backed up to backups/"

# Development shortcuts
dev: setup format lint test
	@echo "🚀 Development environment ready and validated!"

prod-check: lint test
	@echo "🔍 Production readiness check..."
	@python -c "from src.ai_companion.config.settings import validate_settings; validate_settings(); print('✅ Configuration valid')"
	@echo "✅ Ready for production deployment!"
