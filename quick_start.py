#!/usr/bin/env python3
"""
Quick Start Script for AI Companion System
One-command setup and launch for new users.
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def print_banner():
    """Print welcome banner."""
    print("=" * 60)
    print("🚀 AI Companion System - Quick Start")
    print("=" * 60)
    print("Welcome! This script will help you get started quickly.")
    print()

def check_requirements():
    """Check basic requirements."""
    print("📋 Checking requirements...")
    
    # Check Python version
    if sys.version_info < (3, 9):
        print(f"❌ Python 3.9+ required, found {sys.version_info.major}.{sys.version_info.minor}")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}")
    
    # Check if pip is available
    try:
        subprocess.run([sys.executable, "-m", "pip", "--version"], 
                      capture_output=True, check=True)
        print("✅ pip is available")
    except subprocess.CalledProcessError:
        print("❌ pip not found")
        return False
    
    return True

def setup_environment():
    """Set up the environment."""
    print("⚙️ Setting up environment...")
    
    # Create .env file if it doesn't exist
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        shutil.copy(env_example, env_file)
        print("✅ Created .env file from template")
    
    # Create data directories
    data_dirs = ["data", "data/db", "data/logs", "data/cache"]
    for dir_path in data_dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    print("✅ Data directories created")

def install_dependencies():
    """Install dependencies."""
    print("📦 Installing dependencies...")
    
    try:
        # Install core dependencies
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], check=True)
        print("✅ Dependencies installed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def configure_api_key():
    """Help user configure API key."""
    print("🔑 API Key Configuration")
    print()
    
    env_file = Path(".env")
    if not env_file.exists():
        print("❌ .env file not found")
        return False
    
    # Check if API key is already configured
    with open(env_file, 'r') as f:
        content = f.read()
    
    if "GEMINI_API_KEY=your_gemini_api_key_here" in content:
        print("⚠️ Gemini API key needs to be configured")
        print()
        print("To get your API key:")
        print("1. Visit: https://makersuite.google.com/app/apikey")
        print("2. Create a new API key")
        print("3. Copy the key")
        print()
        
        api_key = input("Enter your Gemini API key (or press Enter to skip): ").strip()
        
        if api_key:
            # Update the .env file
            updated_content = content.replace(
                "GEMINI_API_KEY=your_gemini_api_key_here",
                f"GEMINI_API_KEY={api_key}"
            )
            
            with open(env_file, 'w') as f:
                f.write(updated_content)
            
            print("✅ API key configured")
            return True
        else:
            print("⚠️ Skipping API key configuration")
            print("   You can edit .env file manually later")
            return False
    else:
        print("✅ API key appears to be configured")
        return True

def run_health_check():
    """Run system health check."""
    print("🏥 Running health check...")
    
    try:
        result = subprocess.run([
            sys.executable, "scripts/health_check.py"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Health check passed")
            return True
        else:
            print("⚠️ Health check found issues:")
            print(result.stdout)
            return False
    except Exception as e:
        print(f"⚠️ Could not run health check: {e}")
        return False

def start_system():
    """Start the AI Companion System."""
    print("🚀 Starting AI Companion System...")
    print()
    print("The system will start in a few seconds...")
    print("Access the web interface at: http://localhost:7860")
    print("API documentation at: http://localhost:8000/docs")
    print()
    print("Press Ctrl+C to stop the system")
    print()
    
    try:
        # Start the system
        subprocess.run([sys.executable, "-m", "ai_companion.main"], cwd="src")
    except KeyboardInterrupt:
        print("\n👋 System stopped by user")
    except Exception as e:
        print(f"\n❌ Error starting system: {e}")
        print("\nTroubleshooting:")
        print("1. Check your .env file configuration")
        print("2. Run: python scripts/health_check.py")
        print("3. Check the logs in data/logs/")

def main():
    """Main quick start function."""
    print_banner()
    
    # Check requirements
    if not check_requirements():
        print("\n❌ Requirements check failed")
        sys.exit(1)
    
    # Setup environment
    setup_environment()
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Dependency installation failed")
        sys.exit(1)
    
    # Configure API key
    api_key_configured = configure_api_key()
    
    # Run health check
    health_ok = run_health_check()
    
    print("\n" + "=" * 60)
    print("📊 SETUP SUMMARY")
    print("=" * 60)
    print(f"✅ Environment setup: Complete")
    print(f"✅ Dependencies: Installed")
    print(f"{'✅' if api_key_configured else '⚠️'} API Key: {'Configured' if api_key_configured else 'Needs configuration'}")
    print(f"{'✅' if health_ok else '⚠️'} Health Check: {'Passed' if health_ok else 'Issues found'}")
    print()
    
    if not api_key_configured:
        print("⚠️ API key not configured. Please:")
        print("1. Edit .env file")
        print("2. Set GEMINI_API_KEY=your_actual_api_key")
        print("3. Run: python quick_start.py")
        print()
        sys.exit(1)
    
    # Ask if user wants to start the system
    start_now = input("Start the AI Companion System now? (y/N): ").strip().lower()
    
    if start_now in ['y', 'yes']:
        start_system()
    else:
        print("\n🎉 Setup complete!")
        print("\nTo start the system later:")
        print("  python -m ai_companion.main")
        print("\nOr use the Makefile:")
        print("  make run")

if __name__ == "__main__":
    main()
